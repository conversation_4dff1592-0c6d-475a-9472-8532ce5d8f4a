# 📧 SMTP Credentials Setup Guide

## Overview
This guide explains how to configure SMTP credentials for the "enviador-automatico" workflow using the Stargety mail server.

## 🔧 SMTP Server Configuration

### Server Details
- **SMTP Host**: `mail.stargety.com`
- **SMTP Port**: `465` (SSL/TLS)
- **Security**: SSL/TLS encryption
- **Authentication**: Required
- **Username**: `<EMAIL>`
- **Password**: `y{wtT8gerEE?M)85d4`
- **From Email**: `<EMAIL>`
- **To Email**: `<EMAIL>`

## 📋 Step-by-Step Setup

### Step 1: Create SMTP Credentials in n8n

1. **Open n8n Settings**
   - Navigate to **Settings** → **Credentials**
   - Click **Add Credential**

2. **Select SMTP Credential Type**
   - Search for "SMTP" or "Email Send"
   - Select **"SMTP"** credential type

3. **Configure SMTP Settings**
   ```
   Credential Name: Stargety SMTP
   Host: mail.stargety.com
   Port: 465
   Security: SSL/TLS
   Username: <EMAIL>
   Password: y{wtT8gerEE?M)85d4
   ```

4. **Test Connection**
   - Click **Test** to verify the connection
   - Ensure the test passes before saving

5. **Save Credentials**
   - Click **Save** to store the credentials securely

### Step 2: Configure the Send Email Node

1. **Open the Workflow**
   - Import the `enviador-automatico-workflow-smtp.json` file
   - Navigate to the "Send Email via SMTP" node

2. **Assign SMTP Credentials**
   - Click on the "Send Email via SMTP" node
   - In the **Credentials** dropdown, select "Stargety SMTP"
   - Verify all other settings are correct:
     ```
     From Email: <EMAIL>
     To Email: ={{ $json.recipient }}
     Subject: ={{ $json.emailSubject }}
     Email Format: HTML
     HTML Content: ={{ $json.emailBody }}
     ```

3. **Configure Additional Options**
   - **Append Attribution**: `false` (already configured)
   - **CC Email**: Leave empty (optional)
   - **BCC Email**: Leave empty (optional)
   - **Reply To**: Leave empty (optional)

### Step 3: Test the Email Configuration

1. **Manual Test**
   - Execute the workflow manually
   - Check each node for successful execution
   - Verify the email is <NAME_EMAIL>

2. **Check Email Content**
   - Verify HTML formatting is correct
   - Confirm all dynamic content is populated
   - Check that timestamps and request IDs are included

3. **Verify SMTP Delivery**
   - Check email headers to confirm SMTP delivery
   - Ensure emails are not marked as spam
   - Verify sender authentication is working

## 🔒 Security Best Practices

### Credential Management
- **Never store passwords in workflow code**
- **Use n8n's credential system** for secure storage
- **Regularly rotate SMTP passwords** if required
- **Limit credential access** to necessary users only

### Email Security
- **Use SSL/TLS encryption** (port 465)
- **Verify SMTP server certificates**
- **Monitor for unauthorized usage**
- **Keep credentials confidential**

### Network Security
- **Ensure n8n can reach mail.stargety.com:465**
- **Check firewall rules** if connection fails
- **Verify DNS resolution** for the SMTP server
- **Use secure network connections**

## 🚨 Troubleshooting

### Common SMTP Issues

#### 1. Authentication Failed
- **Error**: "Authentication failed"
- **Solution**: 
  - Verify username: `<EMAIL>`
  - Verify password: `y{wtT8gerEE?M)85d4`
  - Check for typos in credentials
  - Ensure SMTP server allows the account

#### 2. Connection Timeout
- **Error**: "Connection timeout"
- **Solution**:
  - Verify host: `mail.stargety.com`
  - Verify port: `465`
  - Check firewall settings
  - Ensure SSL/TLS is enabled

#### 3. SSL/TLS Errors
- **Error**: "SSL handshake failed"
- **Solution**:
  - Confirm port 465 with SSL/TLS
  - Check server certificate validity
  - Verify n8n SSL/TLS support
  - Try different security settings if needed

#### 4. Email Not Delivered
- **Error**: "Email sent but not received"
- **Solution**:
  - Check spam/junk folders
  - Verify recipient email: `<EMAIL>`
  - Check email server logs
  - Verify sender reputation

### Debug Steps
1. **Test SMTP credentials** in n8n credential manager
2. **Execute workflow manually** and check each node
3. **Review n8n execution logs** for detailed errors
4. **Check email server logs** if available
5. **Verify network connectivity** to SMTP server

## 📊 Monitoring & Maintenance

### Regular Checks
- **Monitor email delivery rates**
- **Check for failed executions**
- **Verify SMTP server availability**
- **Review credential expiration dates**

### Performance Optimization
- **Monitor execution times**
- **Check retry logic effectiveness**
- **Optimize email content size**
- **Review error handling patterns**

### Maintenance Tasks
- **Update credentials** before expiration
- **Review SMTP server settings** periodically
- **Monitor email reputation** and deliverability
- **Update workflow** as needed for changes

## 🎯 Expected Results

### Successful Configuration
- ✅ SMTP credentials test successfully
- ✅ Workflow executes without errors
- ✅ Emails are <NAME_EMAIL>
- ✅ HTML formatting displays correctly
- ✅ All dynamic content is populated
- ✅ Timestamps and metadata are included

### Email Characteristics
- **Frequency**: Every hour automatically
- **Content**: Random weather questions with AI responses
- **Format**: Professional HTML with styling
- **Sender**: <EMAIL>
- **Recipient**: <EMAIL>
- **Delivery**: Direct SMTP (no OAuth2 required)

## 📞 Support

### Resources
- n8n Documentation: SMTP Email Node
- Stargety Support: For SMTP server issues
- n8n Community: For workflow questions
- Email Deliverability Guides: For delivery issues

### Contact Information
- **SMTP Server**: mail.stargety.com
- **Technical Support**: Check with Stargety provider
- **n8n Support**: Community forums and documentation

---

**Configuration Complete**: Your SMTP email system is now ready for automated weather updates! 🌤️📧
