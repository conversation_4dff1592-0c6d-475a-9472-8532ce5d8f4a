{"name": "enviador-automatico", "nodes": [{"id": "schedule-trigger", "name": "Hourly Weather Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [100, 200], "parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 1}]}}, "continueOnFail": false, "retryOnFail": true, "maxTries": 3, "waitBetweenTries": 1000}, {"id": "weather-question-generator", "name": "Generate Random Weather Question", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [300, 200], "parameters": {"language": "javaScript", "jsCode": "// Generate random weather question for Gemini AI\nconst weatherQuestions = [\n  'What is the current weather in Madrid, Spain? Please provide temperature, humidity, and general conditions.',\n  'How is the weather today in Tokyo, Japan? Include any weather alerts or notable conditions.',\n  'What is the temperature and weather forecast for New York City right now?',\n  'Is it raining in London today? What are the current weather conditions?',\n  'What is the weather forecast for Paris, France for the next 24 hours?',\n  'How is the weather in Sydney, Australia today? Include temperature and conditions.',\n  'What are the current weather conditions in Mexico City, Mexico?',\n  'Is there any severe weather happening in Miami, Florida right now?',\n  'What is the temperature and humidity in Berlin, Germany today?',\n  'How is the weather in São Paulo, Brazil? Any rain or storms expected?'\n];\n\nconst cities = ['Madrid', 'Tokyo', 'New York', 'London', 'Paris', 'Sydney', 'Mexico City', 'Miami', 'Berlin', 'São Paulo'];\nconst randomQuestion = weatherQuestions[Math.floor(Math.random() * weatherQuestions.length)];\nconst randomCity = cities[Math.floor(Math.random() * cities.length)];\n\nconst currentTime = new Date();\nconst timestamp = currentTime.toISOString();\nconst readableTime = currentTime.toLocaleString('en-US', {\n  timeZone: 'UTC',\n  year: 'numeric',\n  month: 'long',\n  day: 'numeric',\n  hour: '2-digit',\n  minute: '2-digit',\n  second: '2-digit'\n});\n\nreturn [{\n  json: {\n    weatherQuestion: randomQuestion,\n    selectedCity: randomCity,\n    timestamp: timestamp,\n    readableTime: readableTime,\n    requestId: `weather-${Date.now()}`\n  }\n}];"}, "continueOnFail": false, "retryOnFail": true, "maxTries": 2, "waitBetweenTries": 1000}, {"id": "gemini-api-call", "name": "Ask Gemini AI", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [500, 200], "parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "specifyBody": "json", "jsonBody": "{\n  \"contents\": [{\n    \"parts\": [{\n      \"text\": \"{{ $json.weatherQuestion }} Please provide a detailed and informative response with current data if available. Include temperature, conditions, and any relevant weather information. Keep the response conversational and helpful.\"\n    }]\n  }],\n  \"generationConfig\": {\n    \"temperature\": 0.7,\n    \"topK\": 40,\n    \"topP\": 0.95,\n    \"maxOutputTokens\": 1024\n  }\n}"}, "continueOnFail": false, "retryOnFail": true, "maxTries": 3, "waitBetweenTries": 2000}, {"id": "process-gemini-response", "name": "Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [700, 200], "parameters": {"language": "javaScript", "jsCode": "// Process Gemini API response and prepare email content\nconst input = items[0].json;\n\n// Extract the AI response from Gemini API\nlet aiResponse = 'No response received from AI';\nif (input.candidates && input.candidates.length > 0) {\n  const candidate = input.candidates[0];\n  if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {\n    aiResponse = candidate.content.parts[0].text;\n  }\n}\n\n// Get the original question from the previous node\nconst originalQuestion = $node['Generate Random Weather Question'].json.weatherQuestion;\nconst timestamp = $node['Generate Random Weather Question'].json.readableTime;\nconst requestId = $node['Generate Random Weather Question'].json.requestId;\n\n// Create email subject with timestamp\nconst emailSubject = `🌤️ Weather Update from Gemini AI - ${new Date().toLocaleDateString()}`;\n\n// Create HTML email body\nconst emailBody = `\n<html>\n<head>\n  <style>\n    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n    .header { background-color: #4285f4; color: white; padding: 20px; text-align: center; }\n    .content { padding: 20px; }\n    .question { background-color: #f8f9fa; padding: 15px; border-left: 4px solid #4285f4; margin: 15px 0; }\n    .response { background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 15px 0; }\n    .footer { background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }\n  </style>\n</head>\n<body>\n  <div class=\"header\">\n    <h1>🌤️ Weather Update from Gemini AI</h1>\n    <p>Automated Weather Information Service</p>\n  </div>\n  \n  <div class=\"content\">\n    <h2>Weather Question Asked:</h2>\n    <div class=\"question\">\n      <strong>❓ Question:</strong> ${originalQuestion}\n    </div>\n    \n    <h2>Gemini AI Response:</h2>\n    <div class=\"response\">\n      ${aiResponse.replace(/\\n/g, '<br>')}\n    </div>\n    \n    <p><strong>📅 Generated on:</strong> ${timestamp} UTC</p>\n    <p><strong>🔍 Request ID:</strong> ${requestId}</p>\n  </div>\n  \n  <div class=\"footer\">\n    <p>This email was automatically generated by n8n workflow \"enviador-automatico\"</p>\n    <p>Powered by Google Gemini 2.5 Flash AI</p>\n  </div>\n</body>\n</html>\n`;\n\nreturn [{\n  json: {\n    emailSubject: emailSubject,\n    emailBody: emailBody,\n    originalQuestion: originalQuestion,\n    aiResponse: aiResponse,\n    timestamp: timestamp,\n    requestId: requestId,\n    recipient: '<EMAIL>'\n  }\n}];"}, "continueOnFail": false, "retryOnFail": true, "maxTries": 2, "waitBetweenTries": 1000}, {"id": "send-gmail", "name": "Send Email via Gmail", "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [900, 200], "parameters": {"authentication": "oAuth2", "resource": "message", "operation": "send", "sendTo": "={{ $json.recipient }}", "subject": "={{ $json.emailSubject }}", "emailType": "html", "message": "={{ $json.emailBody }}"}, "continueOnFail": false, "retryOnFail": true, "maxTries": 3, "waitBetweenTries": 2000}], "connections": {"Hourly Weather Trigger": {"main": [[{"node": "Generate Random Weather Question", "type": "main", "index": 0}]]}, "Generate Random Weather Question": {"main": [[{"node": "Ask Gemini AI", "type": "main", "index": 0}]]}, "Ask Gemini AI": {"main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]}, "Process AI Response": {"main": [[{"node": "Send Email via Gmail", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveExecutionProgress": true, "timezone": "UTC"}, "active": false, "tags": ["automation", "ai", "weather", "email", "gemini"]}