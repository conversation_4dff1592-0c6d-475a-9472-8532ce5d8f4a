# 📧 Enviador Automático - Setup Guide

## Overview
This n8n workflow automatically sends hourly weather updates to your Gmail account using Google Gemini 2.5 Flash AI. The workflow asks random weather questions and emails the AI responses with beautiful HTML formatting.

## 🏗️ Workflow Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Schedule       │    │  Generate       │    │  Ask Gemini    │    │  Process AI     │    │  Send Email     │
│  Trigger        │───▶│  Weather        │───▶│  AI             │───▶│  Response       │───▶│  via Gmail      │
│  (Hourly)       │    │  Question       │    │  (HTTP API)     │    │  (Format)       │    │  (OAuth2)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Prerequisites

### 1. Google Gemini API Key
1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Copy the API key for later use

### 2. SMTP Server Access
1. SMTP server credentials are pre-configured for Stargety mail server
2. Server: mail.stargety.com (Port 465, SSL/TLS)
3. Username: <EMAIL>
4. Password: y{wtT8gerEE?M)85d4
5. No additional setup required - credentials are provided

## 📥 Installation Steps

### Step 1: Import Workflow
1. Open your n8n instance
2. Click "Import from File" or "Import from URL"
3. Upload the `enviador-automatico-workflow-smtp.json` file
4. The workflow will be imported as "enviador-automatico"

### Step 2: Configure Credentials

#### A. Google Gemini API Credential
1. In n8n, go to **Settings** → **Credentials**
2. Click **Add Credential**
3. Search for "HTTP Query Auth" or "Generic Credential Type"
4. Configure:
   - **Name**: `Gemini API Key`
   - **Auth Type**: `Query Auth`
   - **Query Parameter Name**: `key`
   - **Query Parameter Value**: `YOUR_GEMINI_API_KEY`

#### B. SMTP Credential
1. In n8n, go to **Settings** → **Credentials**
2. Click **Add Credential**
3. Search for "SMTP" or "Email Send"
4. Configure:
   - **Name**: `Stargety SMTP`
   - **Host**: `mail.stargety.com`
   - **Port**: `465`
   - **Security**: `SSL/TLS`
   - **Username**: `<EMAIL>`
   - **Password**: `y{wtT8gerEE?M)85d4`
5. Test connection and save

### Step 3: Configure Nodes

#### 1. "Ask Gemini AI" Node
- Click on the "Ask Gemini AI" node
- In **Authentication**, select your "Gemini API Key" credential
- Verify the URL is correct: `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent`

#### 2. "Send Email via SMTP" Node
- Click on the "Send Email via SMTP" node
- In **Credentials**, select your "Stargety SMTP" credential
- Verify configuration:
  - **From Email**: `<EMAIL>`
  - **To Email**: `<EMAIL>`
  - **Email Format**: `HTML`

### Step 4: Test the Workflow
1. Click **Execute Workflow** to test manually
2. Check each node for successful execution
3. Verify you receive the test email
4. Review the email formatting and content

### Step 5: Activate the Workflow
1. Click the **Active** toggle to enable automatic execution
2. The workflow will now run every hour
3. Monitor executions in the **Executions** tab

## 🎯 Features

### ✅ What This Workflow Does
- **Hourly Triggers**: Automatically runs every hour
- **Random Questions**: Asks 10 different weather questions about various cities
- **AI Integration**: Uses Google Gemini 2.5 Flash for intelligent responses
- **Beautiful Emails**: Sends HTML-formatted emails with styling
- **Error Handling**: Includes retry logic and error recovery
- **Timestamps**: Includes generation time and request IDs
- **Production Ready**: Configured for reliability and monitoring

### 📧 Email Content
Each email includes:
- **Subject**: "🌤️ Weather Update from Gemini AI - [Date]"
- **Weather Question**: The random question asked to AI
- **AI Response**: Detailed weather information from Gemini
- **Timestamp**: When the request was generated
- **Request ID**: Unique identifier for tracking
- **Professional Styling**: Clean HTML layout with colors and formatting

### 🌍 Supported Cities
The workflow randomly asks about weather in:
- Madrid, Spain
- Tokyo, Japan  
- New York City, USA
- London, UK
- Paris, France
- Sydney, Australia
- Mexico City, Mexico
- Miami, Florida
- Berlin, Germany
- São Paulo, Brazil

## 🔒 Security Best Practices

1. **API Keys**: Store in n8n credentials, never in workflow code
2. **SMTP SSL/TLS**: Use encrypted SMTP connection (port 465)
3. **Credentials**: Store SMTP credentials securely in n8n
4. **Monitoring**: Enable execution logging for security auditing

## 🚨 Troubleshooting

### Common Issues

#### 1. Gemini API Errors
- **Error**: 401 Unauthorized
- **Solution**: Verify API key is correct and has proper permissions
- **Check**: API key is active in Google AI Studio

#### 2. SMTP Authentication Errors
- **Error**: SMTP authentication failed
- **Solution**: Verify SMTP credentials are correct
- **Check**: Username: <EMAIL>, Password: y{wtT8gerEE?M)85d4

#### 3. Workflow Not Triggering
- **Error**: Schedule not working
- **Solution**: Ensure workflow is **Active** (toggle on)
- **Check**: n8n instance is running continuously

#### 4. Email Not Received
- **Error**: No email in inbox
- **Solution**: Check spam folder and Gmail filters
- **Check**: Recipient email address is correct

### Debug Steps
1. **Manual Test**: Run workflow manually first
2. **Check Logs**: Review execution logs for errors
3. **Node by Node**: Test each node individually
4. **Credentials**: Verify all credentials are properly configured

## 📊 Monitoring

### Execution Tracking
- Monitor in n8n **Executions** tab
- Check success/failure rates
- Review execution times
- Analyze error patterns

### Email Delivery
- Verify emails are being received
- Check email formatting and content
- Monitor for delivery issues

## 🔄 Customization Options

### Change Email Frequency
Edit the "Hourly Weather Trigger" node:
- **Daily**: Change to `{"field": "days", "daysInterval": 1}`
- **Every 6 hours**: Change to `{"field": "hours", "hoursInterval": 6}`
- **Weekly**: Change to `{"field": "weeks", "weeksInterval": 1}`

### Add More Cities
Edit the "Generate Random Weather Question" node:
- Add more cities to the `cities` array
- Add corresponding questions to `weatherQuestions` array

### Change Email Recipient
Edit the "Process AI Response" node:
- Change `recipient: '<EMAIL>'` to your desired email

### Customize Email Styling
Edit the HTML template in "Process AI Response" node:
- Modify CSS styles
- Change colors and fonts
- Add company branding

## 📋 Workflow Validation Results

✅ **Workflow Status**: Valid and Ready for Deployment
✅ **Total Nodes**: 5 nodes configured
✅ **Connections**: 4 valid connections
✅ **Error Handling**: Retry logic implemented
✅ **Expressions**: All expressions validated
✅ **Security**: SMTP SSL/TLS and API key authentication

## 🎉 Success!

Your automated weather email system is now ready! You'll receive hourly weather updates with AI-generated content directly in your Gmail inbox.

For support or customization requests, refer to the n8n documentation or community forums.
