# 📧 Enviador Automático - Setup Guide

## Overview
This n8n workflow automatically sends hourly weather updates to your Gmail account using Google Gemini 2.5 Flash AI. The workflow asks random weather questions and emails the AI responses with beautiful HTML formatting.

## 🏗️ Workflow Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Schedule       │    │  Generate       │    │  Ask Gemini    │    │  Process AI     │    │  Send Email     │
│  Trigger        │───▶│  Weather        │───▶│  AI             │───▶│  Response       │───▶│  via Gmail      │
│  (Hourly)       │    │  Question       │    │  (HTTP API)     │    │  (Format)       │    │  (OAuth2)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Prerequisites

### 1. Google Gemini API Key
1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Copy the API key for later use

### 2. Gmail OAuth2 Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Gmail API
4. Create OAuth2 credentials (Desktop application)
5. Download the credentials JSON file

## 📥 Installation Steps

### Step 1: Import Workflow
1. Open your n8n instance
2. Click "Import from File" or "Import from URL"
3. Upload the `enviador-automatico-workflow.json` file
4. The workflow will be imported as "enviador-automatico"

### Step 2: Configure Credentials

#### A. Google Gemini API Credential
1. In n8n, go to **Settings** → **Credentials**
2. Click **Add Credential**
3. Search for "HTTP Query Auth" or "Generic Credential Type"
4. Configure:
   - **Name**: `Gemini API Key`
   - **Auth Type**: `Query Auth`
   - **Query Parameter Name**: `key`
   - **Query Parameter Value**: `YOUR_GEMINI_API_KEY`

#### B. Gmail OAuth2 Credential
1. In n8n, go to **Settings** → **Credentials**
2. Click **Add Credential**
3. Search for "Gmail OAuth2 API"
4. Configure:
   - **Name**: `Gmail OAuth2`
   - **Client ID**: From your Google Cloud Console
   - **Client Secret**: From your Google Cloud Console
   - **Scope**: `https://www.googleapis.com/auth/gmail.send`
5. Complete OAuth2 authorization flow

### Step 3: Configure Nodes

#### 1. "Ask Gemini AI" Node
- Click on the "Ask Gemini AI" node
- In **Authentication**, select your "Gemini API Key" credential
- Verify the URL is correct: `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent`

#### 2. "Send Email via Gmail" Node
- Click on the "Send Email via Gmail" node
- In **Authentication**, select your "Gmail OAuth2" credential
- Verify recipient email is set to: `<EMAIL>`

### Step 4: Test the Workflow
1. Click **Execute Workflow** to test manually
2. Check each node for successful execution
3. Verify you receive the test email
4. Review the email formatting and content

### Step 5: Activate the Workflow
1. Click the **Active** toggle to enable automatic execution
2. The workflow will now run every hour
3. Monitor executions in the **Executions** tab

## 🎯 Features

### ✅ What This Workflow Does
- **Hourly Triggers**: Automatically runs every hour
- **Random Questions**: Asks 10 different weather questions about various cities
- **AI Integration**: Uses Google Gemini 2.5 Flash for intelligent responses
- **Beautiful Emails**: Sends HTML-formatted emails with styling
- **Error Handling**: Includes retry logic and error recovery
- **Timestamps**: Includes generation time and request IDs
- **Production Ready**: Configured for reliability and monitoring

### 📧 Email Content
Each email includes:
- **Subject**: "🌤️ Weather Update from Gemini AI - [Date]"
- **Weather Question**: The random question asked to AI
- **AI Response**: Detailed weather information from Gemini
- **Timestamp**: When the request was generated
- **Request ID**: Unique identifier for tracking
- **Professional Styling**: Clean HTML layout with colors and formatting

### 🌍 Supported Cities
The workflow randomly asks about weather in:
- Madrid, Spain
- Tokyo, Japan  
- New York City, USA
- London, UK
- Paris, France
- Sydney, Australia
- Mexico City, Mexico
- Miami, Florida
- Berlin, Germany
- São Paulo, Brazil

## 🔒 Security Best Practices

1. **API Keys**: Store in n8n credentials, never in workflow code
2. **OAuth2**: Use OAuth2 for Gmail instead of app passwords
3. **Scopes**: Use minimal required Gmail scopes
4. **Monitoring**: Enable execution logging for security auditing

## 🚨 Troubleshooting

### Common Issues

#### 1. Gemini API Errors
- **Error**: 401 Unauthorized
- **Solution**: Verify API key is correct and has proper permissions
- **Check**: API key is active in Google AI Studio

#### 2. Gmail Authentication Errors  
- **Error**: OAuth2 failed
- **Solution**: Re-authorize Gmail credentials
- **Check**: Gmail API is enabled in Google Cloud Console

#### 3. Workflow Not Triggering
- **Error**: Schedule not working
- **Solution**: Ensure workflow is **Active** (toggle on)
- **Check**: n8n instance is running continuously

#### 4. Email Not Received
- **Error**: No email in inbox
- **Solution**: Check spam folder and Gmail filters
- **Check**: Recipient email address is correct

### Debug Steps
1. **Manual Test**: Run workflow manually first
2. **Check Logs**: Review execution logs for errors
3. **Node by Node**: Test each node individually
4. **Credentials**: Verify all credentials are properly configured

## 📊 Monitoring

### Execution Tracking
- Monitor in n8n **Executions** tab
- Check success/failure rates
- Review execution times
- Analyze error patterns

### Email Delivery
- Verify emails are being received
- Check email formatting and content
- Monitor for delivery issues

## 🔄 Customization Options

### Change Email Frequency
Edit the "Hourly Weather Trigger" node:
- **Daily**: Change to `{"field": "days", "daysInterval": 1}`
- **Every 6 hours**: Change to `{"field": "hours", "hoursInterval": 6}`
- **Weekly**: Change to `{"field": "weeks", "weeksInterval": 1}`

### Add More Cities
Edit the "Generate Random Weather Question" node:
- Add more cities to the `cities` array
- Add corresponding questions to `weatherQuestions` array

### Change Email Recipient
Edit the "Process AI Response" node:
- Change `recipient: '<EMAIL>'` to your desired email

### Customize Email Styling
Edit the HTML template in "Process AI Response" node:
- Modify CSS styles
- Change colors and fonts
- Add company branding

## 📋 Workflow Validation Results

✅ **Workflow Status**: Valid and Ready for Deployment
✅ **Total Nodes**: 5 nodes configured
✅ **Connections**: 4 valid connections
✅ **Error Handling**: Retry logic implemented
✅ **Expressions**: All expressions validated
✅ **Security**: OAuth2 and API key authentication

## 🎉 Success!

Your automated weather email system is now ready! You'll receive hourly weather updates with AI-generated content directly in your Gmail inbox.

For support or customization requests, refer to the n8n documentation or community forums.
