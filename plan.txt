usa el MCP de n8n para crear un workflow que envie un email a mi cuenta de gmail, usando el trigger de tiempo de n8n.

crea un nuevo workflow con el nombre enviardor-automatico



Available Options:
1. Trigger Options:

Schedule Trigger: User-friendly interface with preset intervals (hourly)
aska random question to gemini about the weather today.

2. Email Sending Options:


Send Email Node: Uses SMTP (requires Gmail SMTP settings)
3. Content Options:


Dynamic content with gemini2.5 flash outputs

Questions for Configuration:

Email Frequency: How often should emails be sent?
Daily, Every hour


Email Content: What should the emails contain?
Subject line and 
Message body that contains the AI summary
Should it include timestamp/date? yes
Any dynamic information? the topic of the conversation
Authentication Method:
Gmail API (OAuth2 - more secure, requires Google app setup)

Your Gmail Address: What's the recipient email address? <EMAIL>

Error Handling: Should the workflow:
Retry on failure
