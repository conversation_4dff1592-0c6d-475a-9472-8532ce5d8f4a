usa el MCP de n8n para crear un workflow que envie un email a mi cuenta de gmail, usando el trigger de tiempo de n8n.

crea un nuevo workflow con el nombre enviardor-automatico



Available Options:
1. Trigger Options:

Schedule Trigger: User-friendly interface with preset intervals (daily, weekly, hourly, etc.)
Cron Trigger: More flexible with custom cron expressions
2. Email Sending Options:

Gmail Node: Uses Gmail API (requires OAuth2 authentication)
Send Email Node: Uses SMTP (requires Gmail SMTP settings)
3. Content Options:

Static content (same email every time)
Dynamic content with timestamps, system info, etc.
Questions for Configuration:
Email Frequency: How often should emails be sent?
Every hour
Daily (what time?)
Weekly (which day and time?)
Custom schedule
Email Content: What should the emails contain?
Subject line
Message body
Should it include timestamp/date?
Any dynamic information?
Authentication Method:
Gmail API (OAuth2 - more secure, requires Google app setup)
SMTP (App password - simpler setup, requires Gmail app password)
Your Gmail Address: What's the recipient email address?
Error Handling: Should the workflow:
Retry on failure?
Continue on errors?
Stop on first error?