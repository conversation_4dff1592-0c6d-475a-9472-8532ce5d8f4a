# n8n Workflow Automation Platform - Complete Configuration Guide

## 🎯 Overview
This document provides the complete configuration for the n8n workflow automation platform running on VPS (**************) with domain vps.stargety.com.

## ✅ Current Working Configuration

### **Access URLs**
1. **Primary HTTPS Access (Recommended)**: https://vps.stargety.com
   - SSL/TLS encrypted with automatic Let's Encrypt certificates
   - HTTP/2 and HTTP/3 support
   - Reverse proxy through Caddy

2. **Direct IP Access (Port 5678)**: http://**************:5678/
   - Direct access to n8n container
   - No SSL encryption
   - Useful for troubleshooting

3. **Alternative Direct Access (Port 8080)**: http://**************:8080/
   - Alternative direct access port
   - No SSL encryption
   - Additional access method

### **Container Configuration**

#### n8n Container (`n8n_new`)
```bash
docker run -d --name n8n_new --restart always \
  -p 5678:5678 \
  -p 8080:5678 \
  -v ~/.n8n:/home/<USER>/.n8n \
  -e N8N_HOST=vps.stargety.com \
  -e N8N_PORT=5678 \
  -e N8N_PROTOCOL=https \
  -e N8N_SECURE_COOKIE=false \
  -e N8N_BASIC_AUTH_ACTIVE=true \
  -e N8N_RUNNERS_ENABLED=true \
  -e N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true \
  docker.n8n.io/n8nio/n8n:latest
```

**Environment Variables Explained:**
- `N8N_HOST=vps.stargety.com`: Domain name for the n8n instance
- `N8N_PORT=5678`: Internal port for n8n
- `N8N_PROTOCOL=https`: Protocol for external access via domain
- `N8N_SECURE_COOKIE=false`: Allows HTTP access for direct IP connections
- `N8N_BASIC_AUTH_ACTIVE=true`: Enables basic authentication
- `N8N_RUNNERS_ENABLED=true`: Enables task runners (recommended)
- `N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true`: Automatically fixes file permissions

#### Caddy Container (`caddy_new`)
```bash
docker run -d --name caddy_new --restart always \
  --network host \
  -v /etc/caddy/Caddyfile:/etc/caddy/Caddyfile \
  -v caddy_data:/data \
  -v caddy_config:/config \
  caddy:latest
```

**Network Configuration:**
- Uses `--network host` for optimal performance
- Automatically handles SSL certificate generation and renewal
- Listens on ports 80 (HTTP redirect) and 443 (HTTPS)

### **Caddyfile Configuration**
```caddy
vps.stargety.com {
    # Route for Documenso
    handle_path /documenso/* {
        reverse_proxy http://localhost:3000 {
            header_up Host {host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }

        header Access-Control-Allow-Origin https://vps.stargety.com 
        header Access-Control-Allow-Credentials true
        header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        header Access-Control-Allow-Headers "Content-Type, Authorization"
    }

    # All other traffic goes to n8n
    handle {
        reverse_proxy http://127.0.0.1:5678 {
            header_up Host {host}
            header_up X-Forwarded-For {remote_host}
        }
    }
}
```

### **Firewall Configuration (UFW)**
```bash
# Required ports for n8n and web access
ufw allow 22/tcp      # SSH
ufw allow 80/tcp      # HTTP (Caddy redirect)
ufw allow 443/tcp     # HTTPS (Caddy SSL)
ufw allow 5678/tcp    # n8n direct access
ufw allow 8080/tcp    # n8n alternative access
```

### **Port Mapping Summary**
| Port | Service | Purpose | Access |
|------|---------|---------|---------|
| 80 | Caddy | HTTP→HTTPS redirect | External |
| 443 | Caddy | HTTPS with SSL | External |
| 5678 | n8n | Direct HTTP access | External |
| 8080 | n8n | Alternative HTTP access | External |
| 5679 | n8n | Task Broker (internal) | Internal only |

## 🔧 Technical Details

### **Version Information**
- **n8n Version**: 1.102.3 (latest)
- **Caddy Version**: Latest
- **Docker**: Container-based deployment

### **Features Enabled**
- ✅ SSL/TLS encryption with automatic certificates
- ✅ HTTP/2 and HTTP/3 support
- ✅ Task runners for improved performance
- ✅ Basic authentication
- ✅ Automatic file permission management
- ✅ Container auto-restart on failure
- ✅ Multiple access methods

### **Data Persistence**
- n8n data stored in: `~/.n8n/` (host directory)
- Caddy certificates stored in: Docker volumes (`caddy_data`, `caddy_config`)

## 🚨 Troubleshooting Guide

### **Common Issues and Solutions**

#### 1. Website Not Accessible Externally
**Symptoms**: Timeouts when accessing https://vps.stargety.com
**Solution**: Check firewall rules
```bash
ufw status
ufw allow 80/tcp
ufw allow 443/tcp
```

#### 2. Secure Cookie Errors
**Symptoms**: "Your n8n server is configured to use a secure cookie" error
**Solution**: Ensure `N8N_SECURE_COOKIE=false` is set in container environment

#### 3. SSL Certificate Issues
**Symptoms**: SSL/TLS errors or certificate warnings
**Solution**: Check Caddy logs and restart container
```bash
docker logs caddy_new
docker restart caddy_new
```

#### 4. Container Not Starting
**Symptoms**: Container exits immediately
**Solution**: Check container logs
```bash
docker logs n8n_new
docker logs caddy_new
```

### **Diagnostic Commands**
```bash
# Check container status
docker ps

# Check port bindings
netstat -tlnp | grep -E ":(80|443|5678|8080)"

# Test internal connectivity
curl -I http://localhost:5678
curl -I https://vps.stargety.com

# Test external connectivity
curl -I http://**************:5678
curl -I https://vps.stargety.com
```

## 📋 Maintenance Tasks

### **Regular Maintenance**
1. **Update n8n**: Pull latest image and recreate container
2. **Monitor logs**: Check for errors or warnings
3. **Backup data**: Backup `~/.n8n/` directory
4. **Certificate renewal**: Automatic via Caddy (no action needed)

### **Container Recreation Commands**
```bash
# Stop and remove old container
docker stop n8n_new && docker rm n8n_new

# Create new container with latest image
docker run -d --name n8n_new --restart always \
  -p 5678:5678 -p 8080:5678 \
  -v ~/.n8n:/home/<USER>/.n8n \
  -e N8N_HOST=vps.stargety.com \
  -e N8N_PORT=5678 \
  -e N8N_PROTOCOL=https \
  -e N8N_SECURE_COOKIE=false \
  -e N8N_BASIC_AUTH_ACTIVE=true \
  -e N8N_RUNNERS_ENABLED=true \
  -e N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true \
  docker.n8n.io/n8nio/n8n:latest
```

## 🔐 Security Considerations

### **Current Security Measures**
- ✅ HTTPS encryption for domain access
- ✅ Basic authentication enabled
- ✅ Firewall configured with minimal required ports
- ✅ Container isolation
- ✅ Automatic security updates via container recreation

### **Recommendations**
1. **Regular Updates**: Keep containers updated with latest security patches
2. **Strong Authentication**: Configure strong basic auth credentials
3. **Network Segmentation**: Consider VPN access for administrative tasks
4. **Monitoring**: Implement log monitoring for security events

## 📊 Performance Optimization

### **Current Optimizations**
- ✅ Task runners enabled for better performance
- ✅ HTTP/2 and HTTP/3 for faster connections
- ✅ Host networking for Caddy (reduced latency)
- ✅ Proper caching headers set

### **Monitoring**
- Monitor container resource usage: `docker stats`
- Check n8n performance metrics in the web interface
- Monitor SSL certificate expiration (automatic renewal)

---

**Last Updated**: July 16, 2025
**Configuration Status**: ✅ Fully Operational
**Next Review**: Monthly maintenance check recommended
